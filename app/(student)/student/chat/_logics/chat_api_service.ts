import { useApi } from '@/hooks/useRequest';
import { CreateChatGroupRequest, ChatGroup, Course, ChatMember, ChatMessage } from './chat_types';

export const useChatApiService = () => {
  const { request, loading, error } = useApi();

  const createChatGroup = async (data: CreateChatGroupRequest): Promise<ChatGroup | null> => {
    try {
      const response = await request('POST', '/chat-group/', data);
      if (response && response.status === 200) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('Error creating chat group:', error);
      return null;
    }
  };

  const fetchStudentCourses = async (): Promise<Course[] | null> => {
    try {
      const response = await request('GET', '/protected/me', null, '');

      if (response && response.status === 200) {
        if (response.data?.data?.courses) {
          const apiCourses = response.data.data.courses || [];
          const mappedCourses: Course[] = apiCourses.map((course: any) => ({
            id: course.id,
            coursename: course.name || course.coursename || 'Unnamed Course'
          }));
          return mappedCourses;
        } else {
          return [];
        }
      } else {
        console.error('Request failed with status:', response?.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching student courses:', error);
      return [];
    }
  };

  // Fetch chat groups with their latest message
  const fetchChatGroups = async (): Promise<ChatGroup[]> => {
    try {
      const response = await request('GET', '/chat-group/', null, '');
      if (response && response.status === 200) {
        if (response.data?.data?.chat_groups) {
          const chatGroups = response.data.data.chat_groups;
          
          // Fetch the latest message for each group
          const groupsWithMessages = await Promise.all(chatGroups.map(async (group: any) => {
            try {
              const messagesResponse = await request('GET', `/chats/groups/${group.id}/messages?limit=1`, null, '');
              let lastMessage = null;
              
              if (messagesResponse?.data?.data?.messages?.[0]) {
                const msg = messagesResponse.data.data.messages[0];
                // Remove 'User:' prefix if it exists at the start of the message
                const cleanContent = msg.content.startsWith('User:') 
                  ? msg.content.substring(5).trim() 
                  : msg.content;
                lastMessage = {
                  content: cleanContent,
                  timestamp: msg.created_at,
                  sender_name: msg.sender_name || 'User'
                };
              }
              
              return {
                ...group,
                last_message: lastMessage
              };
            } catch (error) {
              console.error(`Error fetching messages for group ${group.id}:`, error);
              return group; 
            }
          }));
          
          // Map the response to our ChatGroup interface
          const mappedGroups: ChatGroup[] = groupsWithMessages.map((group: any) => ({
            id: group.id,
            name: group.name,
            description: group.description || '',
            type: group.type || 'group',
            course_id: group.course_id || '',
            is_private: group.is_private || false,
            created_at: group.created_at,
            member_count: group.member_count || 0,
            members: [],
            last_message: group.last_message || null
          }));

          return mappedGroups;
        } else {
          return [];
        }
      } else {
        console.error('Failed to fetch chat groups with status:', response?.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching chat groups:', error);
      return [];
    }
  };

  // Combined function to fetch both groups
  const fetchAllChatGroupsAndPages = async (): Promise<ChatGroup[]> => {
    try {
      const [chatGroups] = await Promise.all([
        fetchChatGroups(),
      ]);

      console.log('Chat groups:', chatGroups);
      return chatGroups;
    } catch (error) {
      console.error('Error fetching all groups and pages:', error);
      return [];
    }
  };

  const fetchChatGroupMembers = async (chatGroupId: string): Promise<ChatMember[]> => {
    try {
      const endpoint = `/chat-group/${chatGroupId}/members`;
      const response = await request('GET', endpoint, null, '');

      if (response && response.status === 200) {
        if (response.data?.data?.members) {
          const members = response.data.data.members;
          const mappedMembers: ChatMember[] = members.map((member: any) => ({
            id: member.user_id || member.id, 
            name: member.name || 'Unknown User',
            avatar: member.profile_image_path || '/logo.png',
            is_admin: member.is_admin || false,
            user_type: member.user_type
          }));

          // Deduplicate members
          const finalMembers = mappedMembers.filter((member, index, self) =>
            index === self.findIndex(m => m.id === member.id)
          );
          return finalMembers;
        } else {
          return [];
        }
      } else {
        console.error('Failed to fetch chat group members with status:', response?.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching chat group members:', error);
      return [];
    }
  };

  // Fetch messages for a specific chat group
  const fetchChatGroupMessages = async (chatGroupId: string, page: number = 1, limit: number = 50): Promise<{messages: ChatMessage[], pagination: any}> => {
    try {
      const response = await request('GET', `/chats/groups/${chatGroupId}/messages?page=${page}&limit=${limit}`, null, '');
      
      if (response && response.status === 200) {
        if (response.data?.data?.messages) {
          const apiMessages = response.data.data.messages;
          const mappedMessages: ChatMessage[] = apiMessages.map((msg: any) => ({
            id: msg._id,
            roomId: msg.chat_group_id,
            senderId: msg.sender_id,
            senderType: msg.sender_type,
            content: msg.content,
            messageType: msg.message_type,
            attachments: msg.attachments || [],
            timestamp: new Date(msg.created_at),
            readBy: msg.read_by || []
          }));
          
          const sortedMessages = mappedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
          return {
            messages: sortedMessages,
            pagination: response.data.data.pagination
          };
        }
      }
      return { messages: [], pagination: null };
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      return { messages: [], pagination: null };
    }
  };

  // Leave a chat group
  const leaveChatGroup = async (chatGroupId: string): Promise<boolean> => {
    try {
      const response = await request('PUT', `/chat-group/${chatGroupId}/member/leave`, null, '');
      return response && response.status === 200;
    } catch (error) {
      console.error('Error leaving chat group:', error);
      return false;
    }
  };

  const sendChatGroupInvite = async (
    groupId: string, 
    inviteeId: string, 
    inviteeType: string, 
    message: string
  ): Promise<boolean> => {
    try {
      const response = await request('POST', `/chat-group/${groupId}/invitations`, {
        invitee_id: inviteeId,
        invitee_type: inviteeType,
        message
      });
      
      return response && response.status === 200;
    } catch (error) {
      console.error('Error sending chat group invite:', error);
      return false;
    }
  };

  // Updated bulk invite function - now sends array directly as request body
  const sendChatGroupBulkInvite = async (
    groupId: string,
    invitations: Array<{ invitee_id: string; invitee_type: string }>,
    message: string
  ): Promise<boolean> => {
    try {
      // Format the invitations array with the message included in each invitation
      const formattedInvitations = invitations.map(invitation => ({
        ...invitation,
        message
      }));

      // Send the array directly as the request body
      const response = await request('POST', `/chat-group/${groupId}/invitations/bulk`, formattedInvitations);
      return response && response.status === 200;
    } catch (error) {
      console.error('Error sending bulk chat group invites:', error);
      return false;
    }
  };

  // Search students by name
  const searchStudents = async (
    query: string,
    page: number = 1,
    limit: number = 10,
    extraParams: Record<string, string | number | boolean> = {}
  ): Promise<any[]> => {
    try {
      const baseParams: Record<string, string> = { page: String(page), limit: String(limit) };
      if (query) {
        baseParams.query = query;
      }
      const extra: Record<string, string> = {};
      Object.entries(extraParams).forEach(([k, v]) => {
        if (v !== undefined && v !== null && v !== '') {
          extra[k] = String(v);
        }
      });

      const params = new URLSearchParams({ ...baseParams, ...extra });
      const response = await request('GET', `/search/students?${params.toString()}`);

      if (response && response.status === 200) {
        const resData = response.data;
        if (Array.isArray(resData)) return resData;
        if (Array.isArray(resData?.data)) return resData.data;
        if (Array.isArray(resData?.data?.students)) return resData.data.students;
      }

      return [];
    } catch (error) {
      console.error('Error searching students:', error);
      return [];
    }
  };

  // Search teachers by name
  const searchTeachers = async (
    query: string,
    page: number = 1,
    limit: number = 10,
    extraParams: Record<string, string | number | boolean> = {}
  ): Promise<any[]> => {
    try {
      const baseParamsT: Record<string, string> = { page: String(page), limit: String(limit) };
      if (query) {
        baseParamsT.query = query;
      }
      const extraT: Record<string, string> = {};
      Object.entries(extraParams).forEach(([k, v]) => {
        if (v !== undefined && v !== null && v !== '') {
          extraT[k] = String(v);
        }
      });
      const params = new URLSearchParams({ ...baseParamsT, ...extraT });
      const response = await request('GET', `/search/teachers?${params.toString()}`);
      if (response && response.status === 200) {
        return response.data?.data?.teachers || [];
      }
      return [];
    } catch (error) {
      console.error('Error searching teachers:', error);
      return [];
    }
  };

  // Mark messages as read
  const markAsRead = async (roomId: string): Promise<boolean> => {
    try {
      const response = await request('POST', '/chats/mark-as-read', { room_id: roomId });
      return response && response.status === 200;
    } catch (error) {
      console.error('Error marking chat as read:', error);
      return false;
    }
  };

  // Mark direct messages as read
  const markDirectAsRead = async (roomId: string): Promise<boolean> => {
    try {
      const response = await request('POST', '/chats/mark-direct-as-read', { room_id: roomId });
      return response && response.status === 200;
    } catch (error) {
      console.error('Error marking direct chat as read:', error);
      return false;
    }
  };

  return {
    createChatGroup,
    fetchStudentCourses,
    fetchChatGroups,
    fetchAllChatGroupsAndPages,
    fetchChatGroupMembers,
    fetchChatGroupMessages,
    leaveChatGroup,
    sendChatGroupInvite,
    sendChatGroupBulkInvite,
    searchStudents,
    searchTeachers,
    markAsRead,
    markDirectAsRead,
    loading,
    error
  };
};