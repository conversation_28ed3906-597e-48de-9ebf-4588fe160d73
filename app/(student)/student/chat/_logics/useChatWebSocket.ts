import { useEffect, useCallback, useRef } from 'react';
import { WebSocketService } from './websocketService';
import { useChatContext } from './chat_context';

export const useChatWebSocket = (roomId: string | null) => {
  const { addMessage, updateUserPresence } = useChatContext();
  const wsService = useRef<WebSocketService>(WebSocketService.getInstance());
  const isConnected = useRef(false);

  const handleNewMessage = useCallback((message: any) => {
    if (message.type === 'chat_message') {
      addMessage({
        id: message.id || Date.now().toString(),
        roomId: message.room_id || roomId || '',
        senderId: message.sender_id,
        senderName: message.sender_name,
        senderType: message.sender_type,
        senderAvatar: message.sender_avatar,
        content: message.content,
        messageType: message.message_type,
        timestamp: new Date(message.timestamp || Date.now()),
        readBy: message.read_by || [],
        isRead: false,
        status: ''
      });
    } else if (message.type === 'presence') {
      updateUserPresence(
        message.chat_group_id || roomId || '',
        message.online_users || []
      );
    } else if (message.type === 'system') {
      console.log('System message:', message.content);
    }
  }, [roomId, addMessage, updateUserPresence]);

  const handleConnectionChange = useCallback(() => {
    isConnected.current = true;
    console.log('WebSocket connected');
  }, []);

  // Connect to WebSocket when roomId changes
  useEffect(() => {
    if (!roomId) return;

    const connect = async () => {
      try {
        await wsService.current.connectToGroupChat(roomId);
        wsService.current.addMessageHandler(handleNewMessage);
        wsService.current.addConnectionHandler(handleConnectionChange);
      } catch (error) {
        console.error('Failed to connect to WebSocket:', error);
      }
    };

    connect();

    return () => {
      wsService.current.removeMessageHandler(handleNewMessage);
      wsService.current.removeConnectionHandler(handleConnectionChange);
      wsService.current.disconnect();
      isConnected.current = false;
    };
  }, [roomId, handleNewMessage, handleConnectionChange]);

  const sendChatMessage = useCallback(async (content: string) => {
    if (!isConnected.current || !roomId) {
      throw new Error('Not connected to chat or no room selected');
    }
    await wsService.current.sendMessage({
      type: 'chat_message',
      content,
      roomId,
      timestamp: new Date().toISOString()
    });
  }, [roomId]);

  return {
    isConnected: isConnected.current,
    sendMessage: sendChatMessage,
  };
};